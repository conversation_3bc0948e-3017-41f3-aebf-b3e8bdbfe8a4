{"name": "sem-360-back", "version": "0.1.0", "private": true, "description": "A Strapi application", "scripts": {"build": "strapi build", "console": "strapi console", "deploy": "strapi deploy", "dev": "strapi develop", "develop": "strapi develop", "start": "node node_modules/@strapi/strapi/bin/strapi.js start", "strapi": "strapi", "upgrade": "npx @strapi/upgrade latest", "upgrade:dry": "npx @strapi/upgrade latest --dry", "db:migrate": "strapi db:migrate", "db:migrate:down": "strapi db:migrate:down", "db:migrate:status": "strapi db:migrate:status", "seeds:validate": "node scripts/seed-manager.js validate", "seeds:help": "node scripts/seed-manager.js help"}, "dependencies": {"@strapi/plugin-cloud": "5.18.1", "@strapi/plugin-documentation": "^5.18.1", "@strapi/plugin-users-permissions": "5.18.1", "@strapi/provider-email-nodemailer": "^5.18.1", "@strapi/strapi": "5.18.1", "axios": "^1.10.0", "better-sqlite3": "^12.2.0", "nodemailer": "^7.0.2", "pg": "8.8.0", "react": "^18.0.0", "react-dom": "^18.0.0", "react-router-dom": "^6.0.0", "styled-components": "^6.0.0"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "typescript": "^5"}, "engines": {"node": ">=18.0.0 <=22.x.x", "npm": ">=6.0.0"}, "strapi": {"uuid": "************************************"}}