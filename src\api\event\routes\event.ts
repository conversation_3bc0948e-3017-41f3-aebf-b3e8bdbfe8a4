//src\api\event\routes\event.js

'use strict';

/**
 * event router - Router personalizado que combina rutas por defecto con rutas personalizadas
 */

import { factories } from '@strapi/strapi';

// Crear el router por defecto para el modelo event
const eventDefaultRouter = factories.createCoreRouter("api::event.event");

/**
 * Función para crear un router personalizado que combina rutas por defecto con rutas adicionales
 * @param {Object} innerRouter - Router por defecto de Strapi
 * @param {Array} routeOverride - Rutas que sobrescriben las por defecto
 * @param {Array} extraRoutes - Rutas adicionales personalizadas
 */
const eventCustomRouter = (innerRouter, routeOverride = [], extraRoutes = []) => {
  let routes;
  return {
    get prefix() {
      return innerRouter.prefix;
    },
    get routes() {
      if (!routes) routes = innerRouter.routes;

      // Mapear rutas existentes y aplicar sobrescrituras si existen
      const newRoutes = routes.map((route) => {
        let found = false;

        routeOverride.forEach((override) => {
          if (
            route.handler === override.handler &&
            route.method === override.method
          ) {
            found = override;
          }
        });

        return found || route;
      });
      
      // Concatenar rutas adicionales personalizadas
      return newRoutes.concat(extraRoutes);
    },
  };
};

// Definir rutas personalizadas adicionales para eventos
const myExtraRoutes = [
  {
    method: 'GET',
    path: '/events/hierarchical-data',
    handler: 'event.getHierarchicalData',
    config: {
      policies: [],
      middlewares: [],
    },
  },
  {
    method: 'POST',
    path: '/events/:id/sessions/:sessionId/attendance',
    handler: 'event.recordAttendance',
    config: {
      policies: [],
      middlewares: [],
    },
  },
  {
    method: 'POST',
    path: '/events/:id/sessions/:sessionId/complete-kiosk',
    handler: 'event.completeKioskSession',
    config: {
      policies: [],
      middlewares: [],
    },
  },
  {
    method: 'PATCH',
    path: '/events/:id/status',
    handler: 'event.updateEventStatus',
    config: {
      policies: [],
      middlewares: [],
    },
  },
  {
    method: 'POST',
    path: '/events/:id/self-register',
    handler: 'event.selfRegister',
    config: {
      auth: false,
      policies: [],
      middlewares: [],
    },
  },
  {
    method: 'GET',
    path: '/users/:userId/events',
    handler: 'event.findForUser',
    config: {
      policies: [],
      middlewares: [],
    },
  },
  {
    method: 'POST',
    path: '/events/:id/justify-absence',
    handler: 'event.justifyAbsence',
    config: {
      policies: [],
      middlewares: [],
    },
  },
  {
    method: 'POST',
    path: '/events/:id/import-participants',
    handler: 'event.importParticipants',
    config: {
      policies: [],
      middlewares: [],
    },
  },
  {
    method: 'POST',
    path: '/events/:id/validate-participant',
    handler: 'event.validateParticipant',
    config: {
      policies: [],
      middlewares: [],
    },
  },
  {
    method: 'POST',
    path: '/events/:eventId/register-participant',
    handler: 'event.registerParticipantAndMarkAttendance',
    config: {
      policies: [],
      middlewares: [],
    },
  },
  {
    method: 'POST',
    path: '/events/:eventId/sessions',
    handler: 'event.addSession',
    config: {
      policies: [],
      middlewares: [],
    },
  },
  {
    method: 'PUT',
    path: '/events/:eventId/sessions/:sessionId',
    handler: 'event.updateSession',
    config: {
      policies: [],
      middlewares: [],
    },
  },
  {
    method: 'DELETE',
    path: '/events/:eventId/sessions/:sessionId',
    handler: 'event.deleteSession',
    config: {
      policies: [],
      middlewares: [],
    },
  },
];

// Rutas que sobrescriben las por defecto (vacío por ahora)
const myOverrideRoutes = [];

// Exportar el router personalizado
export default eventCustomRouter(eventDefaultRouter, myOverrideRoutes, myExtraRoutes);